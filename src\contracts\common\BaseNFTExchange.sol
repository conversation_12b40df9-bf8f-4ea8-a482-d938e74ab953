// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/token/ERC1155/IERC1155.sol";
import "@openzeppelin/contracts/token/common/ERC2981.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "src/contracts/errors/NFTExchangeErrors.sol";
import {BaseCollection} from "src/contracts/common/BaseCollection.sol";
import {Fee} from "src/contracts/common/Fee.sol";
import "src/contracts/events/NFTExchangeEvents.sol";

contract BaseNFTExchange is Ownable {
    struct Listing {
        address contractAddress;
        uint256 tokenId;
        uint256 price;
        address seller;
        uint256 listingDuration;
        uint256 listingStart;
        ListingStatus status;
        uint256 amount;
    }
    // Enum for listing status

    enum ListingStatus {
        Pending,
        Active,
        Sold,
        Failed,
        Cancelled
    }

    // Constants
    uint256 constant TAKER_FEE_BPS = 200; // 2% taker fee in basis points
    uint256 constant BPS_DENOMINATOR = 10000; // Basis points denominator

    // Storage variables
    mapping(bytes32 => Listing) public s_listings; // Maps listingId to Listing
    mapping(address => bytes32[]) public s_listingsByCollection; // Maps contract address to listing IDs
    mapping(address => bytes32[]) public s_listingsBySeller; // Maps seller address to listing IDs
    address public marketplaceWallet; // Wallet for taker fees

    // Events

    constructor(address m_marketplaceWallet) Ownable(msg.sender) {
        if (m_marketplaceWallet == address(0)) revert NFTExchange__InvalidMarketplaceWallet();
        marketplaceWallet = m_marketplaceWallet;
    }

    /**
     * @notice Updates the marketplace wallet address
     * @param m_newMarketplaceWallet The new marketplace wallet address
     */
    function updateMarketplaceWallet(address m_newMarketplaceWallet) external onlyOwner {
        if (m_newMarketplaceWallet == address(0)) revert NFTExchange__InvalidMarketplaceWallet();
        address oldWallet = marketplaceWallet;
        marketplaceWallet = m_newMarketplaceWallet;
        emit MarketplaceWalletUpdated(oldWallet, m_newMarketplaceWallet);
    }

    /**
     * @notice Updates the taker fee
     * @param m_newTakerFee The new taker fee in basis points
     */
    function updateTakerFee(uint256 m_newTakerFee) external onlyOwner {
        if (m_newTakerFee > BPS_DENOMINATOR) revert NFTExchange__InvalidTakerFee();
        uint256 oldFee = TAKER_FEE_BPS;
        emit TakerFeeUpdated(oldFee, m_newTakerFee);
    }

    // Modifier to check active listing
    modifier onlyActiveListing(bytes32 m_listingId) {
        if (s_listings[m_listingId].status != ListingStatus.Active) revert NFTExchange__NFTNotActive();
        if (block.timestamp >= s_listings[m_listingId].listingStart + s_listings[m_listingId].listingDuration) {
            revert NFTExchange__ListingExpired();
        }
        _;
    }

    // Internal function to remove listing from array
    function _removeListingFromArray(bytes32[] storage s_array, bytes32 m_listingId) internal {
        for (uint256 i = 0; i < s_array.length; i++) {
            if (s_array[i] == m_listingId) {
                s_array[i] = s_array[s_array.length - 1];
                s_array.pop();
                break;
            }
        }
    }

    // Internal function to generate listing ID
    function _generateListingId(address m_contractAddress, uint256 m_tokenId, address m_sender)
        internal
        view
        returns (bytes32)
    {
        return keccak256(abi.encodePacked(m_contractAddress, m_tokenId, m_sender, block.timestamp));
    }

    // Internal function to create a single listing
    function _createListing(
        address m_contractAddress,
        uint256 m_tokenId,
        uint256 m_price,
        uint256 m_listingDuration,
        uint256 m_amount,
        bytes32 m_listingId
    ) internal {
        s_listings[m_listingId] = Listing({
            contractAddress: m_contractAddress,
            tokenId: m_tokenId,
            price: m_price,
            seller: msg.sender,
            listingDuration: m_listingDuration,
            listingStart: block.timestamp,
            status: ListingStatus.Active,
            amount: m_amount
        });

        s_listingsByCollection[m_contractAddress].push(m_listingId);
        s_listingsBySeller[msg.sender].push(m_listingId);

        emit NFTListed(m_listingId, m_contractAddress, m_tokenId, msg.sender, m_price);
    }

    // Internal function to distribute payments
    function _distributePayments(
        address m_seller,
        address m_royaltyReceiver,
        uint256 m_price,
        uint256 m_royalty,
        uint256 m_takerFee,
        uint256 m_realityPrice
    ) internal {
        if (msg.value < m_realityPrice) revert NFTExchange__InsufficientPayment();

        (bool sellerSuccess,) = payable(m_seller).call{value: m_price - m_takerFee}("");
        if (!sellerSuccess) revert NFTExchange__TransferToSellerFailed();

        if (m_royalty > 0) {
            (bool royaltySuccess,) = payable(m_royaltyReceiver).call{value: m_royalty}("");
            if (!royaltySuccess) revert NFTExchange__TransferToCreatorFailed();
        }

        (bool feeSuccess,) = payable(marketplaceWallet).call{value: m_takerFee}("");
        if (!feeSuccess) revert NFTExchange__TransferToMarketplaceFailed();

        if (msg.value > m_realityPrice) {
            (bool refundSuccess,) = payable(msg.sender).call{value: msg.value - m_realityPrice}("");
            if (!refundSuccess) revert NFTExchange__RefundFailed();
        }
    }

    // Internal function to finalize listing
    function _finalizeListing(bytes32 m_listingId, address m_contractAddress, address m_seller) internal {
        Listing storage s_listing = s_listings[m_listingId];
        s_listing.status = ListingStatus.Sold;
        _removeListingFromArray(s_listingsByCollection[m_contractAddress], m_listingId);
        _removeListingFromArray(s_listingsBySeller[m_seller], m_listingId);
        emit NFTBought(m_listingId, m_contractAddress, s_listing.tokenId, m_seller, msg.sender, s_listing.price);
    }

    // View function to get floor price (placeholder, needs oracle)
    function getFloorPrice(address m_contractAddress) public view virtual returns (uint256) {
        // Placeholder: Use an oracle (e.g., Chainlink) for real floor price
        return 1 ether; // Example: 1 ETH
    }

    // View function to get top trait price (placeholder, needs oracle)
    function getTopTraitPrice(address m_contractAddress, uint256 m_tokenId) public view virtual returns (uint256) {
        // Placeholder: Use oracle or off-chain data for trait-based pricing
        return getFloorPrice(m_contractAddress) * 120 / 100; // Example: 20% above floor
    }

    // View function to get ladder price (placeholder, needs oracle)
    function getLadderPrice(address m_contractAddress, uint256 m_tokenId) public view virtual returns (uint256) {
        // Placeholder: Use a tiered pricing strategy from off-chain data
        return getFloorPrice(m_contractAddress) * 110 / 100; // Example: 10% above floor
    }

    // View function to get royalty info
    function getRoyaltyInfo(address m_contractAddress, uint256 m_tokenId, uint256 m_salePrice)
        public
        view
        returns (address receiver, uint256 royaltyAmount)
    {
        // try ERC2981(m_contractAddress).royaltyInfo(m_tokenId, m_salePrice) returns (
        //     address m_receiver, uint256 m_royaltyAmount
        // ) {
        //     return (m_receiver, m_royaltyAmount);
        // } catch {
        //     return (address(0), 0); // No royalty if not supported
        // }

        try BaseCollection(m_contractAddress).getFeeContract() returns (Fee m_feeContract) {
            return (m_feeContract.owner(), (m_salePrice * m_feeContract.s_royaltyFee()) / 10000);
        } catch {
            return (address(0), 0); // No royalty if not supported
        }
    }

    // View function to get buyer-sees price (reality price)
    function getBuyerSeesPrice(bytes32 m_listingId) public view returns (uint256) {
        Listing storage s_listing = s_listings[m_listingId];
        (address m_royaltyReceiver, uint256 m_royalty) =
            getRoyaltyInfo(s_listing.contractAddress, s_listing.tokenId, s_listing.price);
        uint256 m_takerFee = (s_listing.price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        return s_listing.price + m_royalty + m_takerFee;
    }

    // View function to get floor difference
    function getFloorDiff(bytes32 m_listingId) public view returns (int256) {
        Listing storage s_listing = s_listings[m_listingId];
        uint256 m_floorPrice = getFloorPrice(s_listing.contractAddress);
        return int256(s_listing.price) - int256(m_floorPrice);
    }

    // View function to get 24-hour volume (placeholder, needs oracle)
    function get24hVolume(address m_contractAddress) public view virtual returns (uint256) {
        // Placeholder: Use oracle or off-chain data for 24h trading volume
        return 10 ether; // Example: 10 ETH
    }

    // View function to get listings by collection
    function getListingsByCollection(address m_contractAddress) public view returns (bytes32[] memory) {
        return s_listingsByCollection[m_contractAddress];
    }

    // View function to get listings by seller
    function getListingsBySeller(address m_seller) public view returns (bytes32[] memory) {
        return s_listingsBySeller[m_seller];
    }

    function getGeneratedListingId(address m_contractAddress, uint256 m_tokenId, address m_sender)
        public
        view
        returns (bytes32)
    {
        return _generateListingId(m_contractAddress, m_tokenId, m_sender);
    }
}
