// SPDX-License-Identifier: MIT

pragma solidity ^0.8.30;

import {Script} from "forge-std/Script.sol";
import {CollectionFactory} from "../src/contracts/core/Collection.sol";

contract DeployCollection is Script {
    function run() public returns (CollectionFactory) {
        vm.startBroadcast();
        CollectionFactory collectionFactory = new CollectionFactory();
        vm.stopBroadcast();
        return collectionFactory;
    }
}
