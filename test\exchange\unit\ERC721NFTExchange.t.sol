// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import "forge-std/Test.sol";
import "test/mock/MockERC721.sol";
import "src/contracts/core/ERC721NFTExchange.sol";
import "src/contracts/errors/NFTExchangeErrors.sol";
import "src/contracts/events/NFTExchangeEvents.sol";
import {Fee} from "src/contracts/common/Fee.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

interface IFee {
    function royaltyInfo(uint256 tokenId, uint256 salePrice)
        external
        view
        returns (address receiver, uint256 royaltyAmount);
    function owner() external view returns (address);
}

contract ERC721NFTExchangeTest is Test {
    ERC721NFTExchange exchange;
    MockERC721 nftContract;
    address owner = address(this);
    address buyer = address(0x456);
    uint256 constant TAKER_FEE_BPS = 200; // 2%
    uint256 constant BPS_DENOMINATOR = 10000;

    // Add receive function to make the contract payable
    receive() external payable {}

    function setUp() public {
        exchange = new ERC721NFTExchange(address(this));
        nftContract = new MockERC721("MockNFT", "MNFT");
        nftContract.mint(address(this), 1);
        nftContract.mint(address(this), 2);
        nftContract.mint(address(this), 3);
    }

    // Test 1: List a single NFT
    function test_ListNFT() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;

        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        vm.expectEmit(true, true, true, true);
        emit NFTListed(listingId, address(nftContract), 1, owner, price);
        exchange.listNFT(address(nftContract), 1, price, duration);
    }

    // Test 2: Batch list NFTs
    function test_BatchListNFT() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](2);
        tokenIds[0] = 1;
        tokenIds[1] = 2;
        uint256[] memory prices = new uint256[](2);
        prices[0] = 1 ether;
        prices[1] = 2 ether;
        uint256 duration = 1 days;

        bytes32 listingId1 = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        bytes32 listingId2 = exchange.getGeneratedListingId(address(nftContract), 2, owner);
        vm.expectEmit(true, true, true, true);
        emit NFTListed(listingId1, address(nftContract), 1, owner, prices[0]);
        vm.expectEmit(true, true, true, true);
        emit NFTListed(listingId2, address(nftContract), 2, owner, prices[1]);
        exchange.batchListNFT(address(nftContract), tokenIds, prices, duration);
    }

    // Test 3: Buy a single NFT
    function test_BuyNFT() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);
    }

    // Test 4: Buy NFT with insufficient payment
    function test_BuyNFT_InsufficientPayment() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);

        uint256 takerFee = (price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice = price + takerFee;
        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        vm.expectRevert(NFTExchange__InsufficientPayment.selector);
        exchange.buyNFT{value: totalPrice - 0.5 ether}(listingId);
    }

    // Test 5: Buy NFT with expired listing
    function test_BuyNFT_ExpiredListing() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);

        vm.warp(block.timestamp + 2 days);

        uint256 takerFee = (price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice = price + takerFee;
        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        vm.expectRevert(NFTExchange__ListingExpired.selector);
        exchange.buyNFT{value: totalPrice}(listingId);
    }

    // Test 6: Cancel a listing
    function test_CancelListing() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);

        vm.expectEmit(true, true, true, false);
        emit NFTCancelled(listingId, address(nftContract), 1);
        exchange.cancelListing(listingId);
    }

    // Test 7: Cancel listing by non-owner
    function test_CancelListing_NotOwner() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);

        vm.prank(buyer);
        vm.expectRevert(NFTExchange__NotTheOwner.selector);
        exchange.cancelListing(listingId);
    }

    // Test 8: Batch cancel listings
    function test_BatchCancelListing() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](2);
        tokenIds[0] = 1;
        tokenIds[1] = 2;
        uint256[] memory prices = new uint256[](2);
        prices[0] = 1 ether;
        prices[1] = 2 ether;
        uint256 duration = 1 days;
        exchange.batchListNFT(address(nftContract), tokenIds, prices, duration);

        bytes32[] memory listingIds = new bytes32[](2);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        listingIds[1] = exchange.getGeneratedListingId(address(nftContract), 2, owner);

        vm.expectEmit(true, true, true, false);
        emit NFTCancelled(listingIds[0], address(nftContract), 1);
        vm.expectEmit(true, true, true, false);
        emit NFTCancelled(listingIds[1], address(nftContract), 2);
        exchange.batchCancelListing(listingIds);
    }

    // Test 9: Batch cancel listings by non-owner
    function test_BatchCancelListing_NotOwner() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](2);
        tokenIds[0] = 1;
        tokenIds[1] = 2;
        uint256[] memory prices = new uint256[](2);
        prices[0] = 1 ether;
        prices[1] = 2 ether;
        uint256 duration = 1 days;
        exchange.batchListNFT(address(nftContract), tokenIds, prices, duration);

        bytes32[] memory listingIds = new bytes32[](2);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        listingIds[1] = exchange.getGeneratedListingId(address(nftContract), 2, owner);

        vm.prank(buyer);
        vm.expectRevert(NFTExchange__NotTheOwner.selector);
        exchange.batchCancelListing(listingIds);
    }

    // Test 10: Batch buy NFTs
    function test_BatchBuyNFT() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](2);
        tokenIds[0] = 1;
        tokenIds[1] = 2;
        uint256[] memory prices = new uint256[](2);
        prices[0] = 1 ether;
        prices[1] = 2 ether;
        uint256 duration = 1 days;
        exchange.batchListNFT(address(nftContract), tokenIds, prices, duration);

        // Buy first NFT
        bytes32 listingId1 = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        uint256 takerFee1 = (prices[0] * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice1 = prices[0] + takerFee1;
        vm.deal(buyer, totalPrice1);
        vm.prank(buyer);
        exchange.buyNFT{value: totalPrice1}(listingId1);
        assertEq(nftContract.ownerOf(1), buyer);

        // Buy second NFT
        bytes32 listingId2 = exchange.getGeneratedListingId(address(nftContract), 2, owner);
        uint256 takerFee2 = (prices[1] * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice2 = prices[1] + takerFee2;
        vm.deal(buyer, totalPrice2);
        vm.prank(buyer);
        exchange.buyNFT{value: totalPrice2}(listingId2);
        assertEq(nftContract.ownerOf(2), buyer);
    }

    // Test 11: Batch buy NFTs with insufficient payment
    function test_BatchBuyNFT_InsufficientPayment() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](2);
        tokenIds[0] = 1;
        tokenIds[1] = 2;
        uint256[] memory prices = new uint256[](2);
        prices[0] = 1 ether;
        prices[1] = 2 ether;
        uint256 duration = 1 days;
        exchange.batchListNFT(address(nftContract), tokenIds, prices, duration);

        bytes32[] memory listingIds = new bytes32[](2);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        listingIds[1] = exchange.getGeneratedListingId(address(nftContract), 2, owner);
        uint256 takerFee1 = (prices[0] * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 takerFee2 = (prices[1] * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice = prices[0] + takerFee1 + prices[1] + takerFee2;

        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        vm.expectRevert(NFTExchange__InsufficientPayment.selector);
        exchange.batchBuyNFT{value: totalPrice - 1 ether}(listingIds);
    }

    // Test 12: Batch buy NFTs from mixed collections (should revert)
    function test_BatchBuyNFT_MixedCollections() public {
        MockERC721 nftContract2 = new MockERC721("MockNFT2", "MNFT2");
        vm.prank(address(this));
        nftContract2.transferOwnership(owner);

        vm.startPrank(owner);
        nftContract2.mint(owner, 4);
        nftContract.setApprovalForAll(address(exchange), true);
        nftContract2.setApprovalForAll(address(exchange), true);

        bytes32 listingId1 = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        bytes32 listingId2 = exchange.getGeneratedListingId(address(nftContract2), 4, owner);
        exchange.listNFT(address(nftContract), 1, 1 ether, 1 days);
        exchange.listNFT(address(nftContract2), 4, 2 ether, 1 days);
        vm.stopPrank();

        bytes32[] memory listingIds = new bytes32[](2);
        listingIds[0] = listingId1;
        listingIds[1] = listingId2;

        uint256 totalPrice = 1 ether + (1 ether * TAKER_FEE_BPS) / BPS_DENOMINATOR + 2 ether
            + (2 ether * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        vm.expectRevert(NFTExchange__ArrayLengthMismatch.selector);
        exchange.batchBuyNFT{value: totalPrice}(listingIds);
    }

    // Test 13: List NFT with zero price
    function test_ListNFT_ZeroPrice() public {
        vm.startPrank(owner);
        nftContract.approve(address(exchange), 1);
        uint256 price = 0;
        uint256 duration = 1 days;
        vm.expectRevert(NFTExchange__PriceMustBeGreaterThanZero.selector);
        exchange.listNFT(address(nftContract), 1, price, duration);
        vm.stopPrank();
    }

    // Test 14: List NFT with zero duration
    function test_ListNFT_ZeroDuration() public {
        vm.startPrank(owner);
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 0;
        vm.expectRevert(NFTExchange__DurationMustBeGreaterThanZero.selector);
        exchange.listNFT(address(nftContract), 1, price, duration);
        vm.stopPrank();
    }

    // Test 15: Buy NFT with no active listing
    function test_BuyNFT_NoListing() public {
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        vm.deal(buyer, 1 ether);
        vm.prank(buyer);
        vm.expectRevert(NFTExchange__NFTNotActive.selector);
        exchange.buyNFT{value: 1 ether}(listingId);
    }

    // Test 16: Cancel already cancelled listing
    function test_CancelListing_AlreadyCancelled() public {
        vm.startPrank(owner);
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);
        exchange.cancelListing(listingId);
        vm.expectRevert(NFTExchange__NFTNotActive.selector);
        exchange.cancelListing(listingId);
        vm.stopPrank();
    }

    // Test 17: Batch buy with empty listing array
    function test_BatchBuyNFT_EmptyArray() public {
        bytes32[] memory listingIds = new bytes32[](0);
        vm.prank(buyer);
        vm.expectRevert(NFTExchange__ArrayLengthMismatch.selector);
        exchange.batchBuyNFT{value: 0}(listingIds);
    }

    // Test 11: Batch buy with zero royalty
    function test_BatchBuyNFT_ZeroRoyalty() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](1);
        uint256[] memory prices = new uint256[](1);

        tokenIds[0] = 1;
        prices[0] = 1 ether;

        exchange.batchListNFT(address(nftContract), tokenIds, prices, 1 days);

        bytes32[] memory listingIds = new bytes32[](1);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);

        uint256 takerFee = (prices[0] * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice = prices[0] + takerFee;

        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        exchange.batchBuyNFT{value: totalPrice}(listingIds);

        assertEq(nftContract.ownerOf(1), buyer);
    }

    // Test 12: Batch buy with failed transfers
    function test_BatchBuyNFT_FailedTransfers() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](1);
        uint256[] memory prices = new uint256[](1);

        tokenIds[0] = 1;
        prices[0] = 1 ether;

        exchange.batchListNFT(address(nftContract), tokenIds, prices, 1 days);

        bytes32[] memory listingIds = new bytes32[](1);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);

        uint256 takerFee = (prices[0] * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 royaltyAmount = prices[0] / 10; // 10% royalty
        uint256 totalPrice = prices[0] + takerFee + royaltyAmount;

        // Create a contract that will fail on receive
        MockFailingReceiver failingReceiver = new MockFailingReceiver();
        vm.deal(address(failingReceiver), totalPrice);

        // Get the fee contract and mock its methods to return the failing receiver
        Fee feeContract = nftContract.feeContract();

        // Mock the fee contract's owner() method to return the failing receiver
        vm.mockCall(
            address(feeContract), abi.encodeWithSelector(Ownable.owner.selector), abi.encode(address(failingReceiver))
        );

        // Mock the fee contract's s_royaltyFee() method to return 1000 (10%)
        vm.mockCall(
            address(feeContract),
            abi.encodeWithSignature("s_royaltyFee()"),
            abi.encode(uint256(1000)) // 10% in basis points
        );

        vm.prank(address(failingReceiver));
        vm.expectRevert(NFTExchange__TransferToCreatorFailed.selector);
        exchange.batchBuyNFT{value: totalPrice}(listingIds);
    }

    // Test 13: Batch cancel expired listing
    function test_BatchCancelListing_Expired() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](1);
        uint256[] memory prices = new uint256[](1);

        tokenIds[0] = 1;
        prices[0] = 1 ether;

        exchange.batchListNFT(address(nftContract), tokenIds, prices, 1 days);

        bytes32[] memory listingIds = new bytes32[](1);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);

        vm.warp(block.timestamp + 2 days);

        vm.expectRevert(NFTExchange__ListingExpired.selector);
        exchange.batchCancelListing(listingIds);
    }

    // Test 14: Batch cancel inactive listing
    function test_BatchCancelListing_Inactive() public {
        nftContract.setApprovalForAll(address(exchange), true);
        uint256[] memory tokenIds = new uint256[](1);
        uint256[] memory prices = new uint256[](1);

        tokenIds[0] = 1;
        prices[0] = 1 ether;

        exchange.batchListNFT(address(nftContract), tokenIds, prices, 1 days);

        bytes32[] memory listingIds = new bytes32[](1);
        listingIds[0] = exchange.getGeneratedListingId(address(nftContract), 1, owner);

        // Cancel the listing first
        exchange.cancelListing(listingIds[0]);

        vm.expectRevert(NFTExchange__NFTNotActive.selector);
        exchange.batchCancelListing(listingIds);
    }

    function test_BatchBuyNFT_WithZeroRoyalty() public {
        // Create a seller address that can receive ETH
        address seller = makeAddr("seller");
        vm.deal(seller, 1 ether);

        // Create NFT with zero royalty
        vm.prank(seller);
        MockERC721 zeroRoyaltyNFT = new MockERC721("Zero", "ZERO");

        vm.startPrank(seller);
        zeroRoyaltyNFT.mint(seller, 1);
        zeroRoyaltyNFT.mint(seller, 2);
        zeroRoyaltyNFT.setApprovalForAll(address(exchange), true);

        uint256[] memory tokenIds = new uint256[](2);
        uint256[] memory prices = new uint256[](2);

        tokenIds[0] = 1;
        tokenIds[1] = 2;
        prices[0] = 1 ether;
        prices[1] = 2 ether;

        exchange.batchListNFT(address(zeroRoyaltyNFT), tokenIds, prices, 1 days);
        vm.stopPrank();

        bytes32[] memory listingIds = new bytes32[](2);
        listingIds[0] = exchange.getGeneratedListingId(address(zeroRoyaltyNFT), 1, seller);
        listingIds[1] = exchange.getGeneratedListingId(address(zeroRoyaltyNFT), 2, seller);

        uint256 totalPrice = prices[0] + (prices[0] * TAKER_FEE_BPS) / BPS_DENOMINATOR + prices[1]
            + (prices[1] * TAKER_FEE_BPS) / BPS_DENOMINATOR;

        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        exchange.batchBuyNFT{value: totalPrice}(listingIds);

        assertEq(zeroRoyaltyNFT.ownerOf(1), buyer);
        assertEq(zeroRoyaltyNFT.ownerOf(2), buyer);
    }

    function test_BuyNFT_WithExactPayment() public {
        nftContract.approve(address(exchange), 1);
        uint256 price = 1 ether;
        uint256 duration = 1 days;
        bytes32 listingId = exchange.getGeneratedListingId(address(nftContract), 1, owner);
        exchange.listNFT(address(nftContract), 1, price, duration);

        uint256 takerFee = (price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 totalPrice = price + takerFee;

        vm.deal(buyer, totalPrice);
        vm.prank(buyer);
        exchange.buyNFT{value: totalPrice}(listingId);

        assertEq(nftContract.ownerOf(1), buyer);
    }
}

// Mock contract that fails on receive
contract MockFailingReceiver {
    receive() external payable {
        revert("Transfer failed");
    }

    function onERC721Received(address operator, address from, uint256 tokenId, bytes calldata data)
        external
        pure
        returns (bytes4)
    {
        return this.onERC721Received.selector;
    }
}
